.loginContainer {
  position: fixed;
  background-color: white;
  background-image: url('../../assets/images/Login.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right;
  overflow-y: auto;
  height: 100vh;
  width: 100vw;
}

.headingContainer {
  margin-top: 30px;
  margin-left: 132px;
  text-align: left;
  display: flex;
  flex-direction: column;
  width: 50%;
}

.heading {
  font-family: var(--font-family-primary);
  font-size: 48px;
  font-weight: var(--font-weight-semibold);
  color: #0e2f51;
}

.subHeading {
  font-family: var(--font-family-primary);
  font-size: 16px;
  font-weight: var(--font-weight-normal);
  color: #667085;
}

.formContainer {
  width: 30%;
  margin-left: 132px;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.vectorLineContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.linkContainer {
  font-family: var(--font-family-primary);
  font-size: 14px;
  color: #0e2f51;
  font-weight: var(--font-weight-medium);
  margin-top: 5px;
  margin-bottom: 10px;
}

.link {
  color: #1b5ea1;
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  margin-left: 4px;
}

.link:hover {
  text-decoration: underline;
}

.checkboxContainer {
  display: flex;
  gap: 10px;
}

.error {
  color: var(--color-error-main);
  font-family: var(--font-family-primary);
  font-size: 14px;
  font-weight: var(--font-weight-normal);
}

.optionalText {
  font-family: var(--font-family-primary);
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  font-style: italic;
  color: #667085;
}

/* Dark theme support */
[data-theme='dark'] .heading {
  color: var(--color-text-primary);
}

[data-theme='dark'] .subHeading {
  color: var(--color-text-secondary);
}

[data-theme='dark'] .linkContainer {
  color: var(--color-text-primary);
}

[data-theme='dark'] .link {
  color: var(--color-primary);
}

[data-theme='dark'] .optionalText {
  color: var(--color-text-secondary);
}
